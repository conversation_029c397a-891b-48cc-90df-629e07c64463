package com.example.financialindicatordaemon.client;

import com.example.financialindicatordaemon.entity.IndicatorData;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MockIndicatorClient implements IndicatorApiClient {
    @Override
    public ResponseEntity<String> checkHealth() {
        return null;
    }

    @Override
    public ResponseEntity<List<IndicatorData>> calculateIndicators(List<CalculateIndicatorsRequest> data) {
        return null;
    }


}
