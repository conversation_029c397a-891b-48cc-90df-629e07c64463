package com.example.financialindicatordaemon.client;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public class IndicatorData {
    private BigDecimal open;
    private BigDecimal high;
    private BigDecimal low;
    private BigDecimal close;
    private BigDecimal volume;
    private BigDecimal marketCap;
    private String timestamp;
    private String name;
    private BigDecimal hl2;
    @JsonProperty("smma_15")
    private BigDecimal smma15;
    @JsonProperty("smma_19")
    private BigDecimal smma19;
    @JsonProperty("smma_25")
    private BigDecimal smma25;
    @JsonProperty("smma_29")
    private BigDecimal smma29;
    private Boolean p1;
    private Boolean p2;
    private Boolean p3;
    private String color;

    public BigDecimal getOpen() {
        return open;
    }

    public void setOpen(BigDecimal open) {
        this.open = open;
    }

    public BigDecimal getHigh() {
        return high;
    }

    public void setHigh(BigDecimal high) {
        this.high = high;
    }

    public BigDecimal getLow() {
        return low;
    }

    public void setLow(BigDecimal low) {
        this.low = low;
    }

    public BigDecimal getClose() {
        return close;
    }

    public void setClose(BigDecimal close) {
        this.close = close;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public BigDecimal getMarketCap() {
        return marketCap;
    }

    public void setMarketCap(BigDecimal marketCap) {
        this.marketCap = marketCap;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getHl2() {
        return hl2;
    }

    public void setHl2(BigDecimal hl2) {
        this.hl2 = hl2;
    }

    public BigDecimal getSmma15() {
        return smma15;
    }

    public void setSmma15(BigDecimal smma15) {
        this.smma15 = smma15;
    }

    public BigDecimal getSmma19() {
        return smma19;
    }

    public void setSmma19(BigDecimal smma19) {
        this.smma19 = smma19;
    }

    public BigDecimal getSmma25() {
        return smma25;
    }

    public void setSmma25(BigDecimal smma25) {
        this.smma25 = smma25;
    }

    public BigDecimal getSmma29() {
        return smma29;
    }

    public void setSmma29(BigDecimal smma29) {
        this.smma29 = smma29;
    }

    public Boolean getP1() {
        return p1;
    }

    public void setP1(Boolean p1) {
        this.p1 = p1;
    }

    public Boolean getP2() {
        return p2;
    }

    public void setP2(Boolean p2) {
        this.p2 = p2;
    }

    public Boolean getP3() {
        return p3;
    }

    public void setP3(Boolean p3) {
        this.p3 = p3;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

}
