package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.client.CalculateIndicatorsRequest;
import com.example.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import com.example.financialindicatordaemon.client.IndicatorApiClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IndicatorApiService {

    private static final Logger logger = LoggerFactory.getLogger(IndicatorApiService.class);

    private final IndicatorApiClient indicatorApiClient;
    private final CmcCandleDataService cmcCandleDataService;

    public IndicatorApiService(@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
                               IndicatorApiClient indicatorApiClient, CmcCandleDataService cmcCandleDataService) {
        this.indicatorApiClient = indicatorApiClient;
        this.cmcCandleDataService = cmcCandleDataService;
    }

    public List<IndicatorData> calculateIndicators(String symbol, String conversionCurrency) {
        logger.info("Sending calculation request to indicator API");

        List<CryptoCandleHistoricalQuote> quotes = cmcCandleDataService.find(symbol, conversionCurrency);
        ResponseEntity<List<IndicatorData>> response = indicatorApiClient.calculateIndicators(quotes.stream()
                .map(
                        quote -> {
                            CalculateIndicatorsRequest request = new CalculateIndicatorsRequest();
                            request.setClose(quote.getQuote().getClose());
                            request.setHigh(quote.getQuote().getHigh());
                            request.setLow(quote.getQuote().getLow());
                            request.setMarketCap(quote.getQuote().getMarketCap());
                            request.setName(quote.getQuote().getName());
                            request.setOpen(quote.getQuote().getOpen());
                            request.setTimestamp(quote.getQuote().getTimestamp());
                            request.setVolume(quote.getQuote().getVolume());
                            return request;
                        }
                ).toList());

        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("Failed to calculate indicators: " + response.getStatusCode());
        }

        return response.getBody();
    }

}
